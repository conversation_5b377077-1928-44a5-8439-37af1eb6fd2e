package com.investment.api.controller;

import com.investment.api.model.*;
import com.investment.model.WatchListItem;
import com.investment.service.WatchListService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * REST controller for watch list operations.
 */
@RestController
@RequestMapping("/api/watchlist")
@Tag(name = "Watch List", description = "Watch list management operations")
public class WatchListController {

    private static final Logger logger = LoggerFactory.getLogger(WatchListController.class);

    private final WatchListService watchListService;

    public WatchListController(WatchListService watchListService) {
        this.watchListService = watchListService;
    }

    @PostMapping
    @Operation(summary = "Add symbol to watch list", description = "Create a new watch list item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Watch list item created successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "409", description = "Symbol already exists in watch list"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListResponse>> createWatchListItem(
            @Valid @RequestBody CreateWatchListRequest request) {
        
        try {
            logger.info("Creating watch list item: {}", request);
            
            WatchListItem item = watchListService.createWatchListItem(request);
            WatchListResponse response = WatchListResponse.fromWatchListItem(item);
            
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Watch list item created successfully", response));

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request for creating watch list item: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));

        } catch (SQLException e) {
            logger.error("Database error creating watch list item", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to create watch list item: " + e.getMessage()));
        }
    }

    @GetMapping
    @Operation(summary = "Get all watch list items", description = "Retrieve all watch list items ordered by display index")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Watch list items retrieved successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<WatchListResponse>>> getAllWatchListItems() {
        try {
            logger.debug("Retrieving all watch list items");
            
            List<WatchListItem> items = watchListService.getAllWatchListItems();
            List<WatchListResponse> responses = items.stream()
                    .map(WatchListResponse::fromWatchListItem)
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(ApiResponse.success("Retrieved " + responses.size() + " watch list items", responses));

        } catch (SQLException e) {
            logger.error("Database error retrieving watch list items", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve watch list items: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get watch list item by ID", description = "Retrieve a specific watch list item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Watch list item found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Watch list item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListResponse>> getWatchListItem(
            @Parameter(description = "Watch list item ID") @PathVariable Long id) {
        
        try {
            logger.debug("Retrieving watch list item by ID: {}", id);
            
            Optional<WatchListItem> item = watchListService.getWatchListItemById(id);
            
            if (item.isPresent()) {
                WatchListResponse response = WatchListResponse.fromWatchListItem(item.get());
                return ResponseEntity.ok(ApiResponse.success("Watch list item found", response));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Watch list item not found: " + id));
            }

        } catch (SQLException e) {
            logger.error("Database error retrieving watch list item by ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve watch list item: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update watch list item", description = "Update an existing watch list item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Watch list item updated successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Watch list item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListResponse>> updateWatchListItem(
            @Parameter(description = "Watch list item ID") @PathVariable Long id,
            @Valid @RequestBody UpdateWatchListRequest request) {
        
        try {
            logger.info("Updating watch list item ID: {} with request: {}", id, request);
            
            if (!request.hasUpdates()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("No update fields provided"));
            }

            WatchListItem item = watchListService.updateWatchListItem(id, request);
            WatchListResponse response = WatchListResponse.fromWatchListItem(item);

            return ResponseEntity.ok(ApiResponse.success("Watch list item updated successfully", response));

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request for updating watch list item ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));

        } catch (SQLException e) {
            logger.error("Database error updating watch list item ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update watch list item: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/performance")
    @Operation(summary = "Update performance metrics", description = "Update performance metrics for a watch list item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Performance metrics updated successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Watch list item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListResponse>> updateWatchListPerformance(
            @Parameter(description = "Watch list item ID") @PathVariable Long id,
            @Parameter(description = "1-month performance percentage") @RequestParam(required = false) BigDecimal oneMonthPerf,
            @Parameter(description = "3-month performance percentage") @RequestParam(required = false) BigDecimal threeMonthPerf,
            @Parameter(description = "6-month performance percentage") @RequestParam(required = false) BigDecimal sixMonthPerf) {
        
        try {
            logger.info("Updating performance for watch list item ID: {}", id);
            
            WatchListItem item = watchListService.updateWatchListPerformance(id, oneMonthPerf, threeMonthPerf, sixMonthPerf);
            WatchListResponse response = WatchListResponse.fromWatchListItem(item);
            
            return ResponseEntity.ok(ApiResponse.success("Performance metrics updated successfully", response));

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request for updating performance for watch list item ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));

        } catch (SQLException e) {
            logger.error("Database error updating performance for watch list item ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update performance metrics: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Remove from watch list", description = "Delete a watch list item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Watch list item deleted successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Watch list item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<String>> deleteWatchListItem(
            @Parameter(description = "Watch list item ID") @PathVariable Long id) {
        
        try {
            logger.info("Deleting watch list item ID: {}", id);
            
            boolean deleted = watchListService.deleteWatchListItem(id);
            
            if (deleted) {
                return ResponseEntity.ok(ApiResponse.success("Watch list item deleted successfully", "Watch list item " + id + " deleted"));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Watch list item not found: " + id));
            }

        } catch (SQLException e) {
            logger.error("Database error deleting watch list item ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to delete watch list item: " + e.getMessage()));
        }
    }

    @PutMapping("/reorder")
    @Operation(summary = "Bulk reorder watch list items", description = "Update display indexes for multiple watch list items")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Watch list items reordered successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<String>> reorderWatchListItems(
            @Valid @RequestBody ReorderWatchListRequest request) {
        
        try {
            logger.info("Reordering watch list items: {}", request);
            
            request.validate();
            watchListService.reorderWatchListItems(request.getIdToIndexMap());
            
            return ResponseEntity.ok(ApiResponse.success("Watch list items reordered successfully",
                    "Reordered " + request.getIdToIndexMap().size() + " items"));

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request for reordering watch list items: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));

        } catch (SQLException e) {
            logger.error("Database error reordering watch list items", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to reorder watch list items: " + e.getMessage()));
        }
    }

    @PostMapping("/recalculate-performance")
    @Operation(summary = "Recalculate performance metrics", description = "Recalculate performance metrics for all watch list items using current OHLCV data")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Performance metrics recalculated successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<RecalculatePerformanceResponse>> recalculatePerformance() {

        try {
            logger.info("Starting performance recalculation for all watch list items");

            RecalculatePerformanceResponse response = watchListService.calculateAndUpdateAllPerformance();

            return ResponseEntity.ok(ApiResponse.success(
                    "Performance recalculation completed: " + response.getSummaryMessage(),
                    response));

        } catch (SQLException e) {
            logger.error("Database error during performance recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to recalculate performance metrics: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error during performance recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error during performance recalculation: " + e.getMessage()));
        }
    }
}
